# -*- coding: utf-8 -*-
"""
Graph API + IMAP + SMTP 公共工具
"""
import base64, json, email, imaplib, ssl, textwrap, aiosmtplib, httpx
from email.message import EmailMessage

TOKEN_URL  = "https://login.microsoftonline.com/consumers/oauth2/v2.0/token"
GRAPH_ROOT = "https://graph.microsoft.com/v1.0"

# ---------- 网络助手（带日志） ----------
async def _post(url, data, headers=None, proxy=None):
    if headers is None:
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
    else:
        headers["Content-Type"] = "application/x-www-form-urlencoded"
    
    if proxy:
        print(f"[POST] 使用代理: {proxy}")
    
    try:
        async with httpx.AsyncClient(proxy=proxy) as c:
            r = await c.post(url, data=data, headers=headers)
        print(f"[POST] {url.split('?')[0]} ➜ {r.status_code}")
        if r.status_code != 200:
            print(textwrap.shorten(r.text, 300))
            return None
        return r.json()
    except Exception as e:
        print(f"[POST] 请求失败: {str(e)}")
        return None

async def _get(url, headers, proxy=None):
    if proxy:
        print(f"[GET] 使用代理: {proxy}")
    
    try:
        async with httpx.AsyncClient(timeout=30, proxy=proxy) as c:
            r = await c.get(url, headers=headers)
        print(f"[GET ] {url.split('?')[0]} ➜ {r.status_code}")
        if r.status_code != 200:
            print(textwrap.shorten(r.text, 300))
            return None
        return r.json()
    except Exception as e:
        print(f"[GET] 请求失败: {str(e)}")
        return None

# ---------- 1) Graph access_token（带 Mail.* scope） ----------
async def token_graph(rt, cid, headers=None, proxy=None):
    data = await _post(TOKEN_URL, {
        "client_id": cid,
        "grant_type": "refresh_token",
        "refresh_token": rt,
        "scope": "https://graph.microsoft.com/.default",
    }, headers=headers, proxy=proxy)
    if not data:
        return None
    at = data.get("access_token")
    if not at or at.count(".") != 2:
        return None
    scope = json.loads(base64.urlsafe_b64decode(at.split(".")[1] + "===")).get("scp", "")
    print("[SCOPE]", scope)
    return at if "Mail.Read" in scope else None

# ---------- 2) 无 scope access_token → XOAUTH2 ----------
async def token_imap(rt, cid, headers=None, proxy=None):
    data = await _post(TOKEN_URL, {
        "client_id": cid,
        "grant_type": "refresh_token",
        "refresh_token": rt,
    }, headers=headers, proxy=proxy)
    return data.get("access_token") if data else None

# ---------- IMAP 连接 ----------
def _imap_conn(email_addr, token, proxy=None):
    # IMAP不直接支持HTTP代理，但可以使用SOCKS代理
    # 如果proxy是SOCKS代理格式，可以配置socket库使用代理
    # 这里简化处理，仅记录代理信息
    if proxy:
        print(f"[IMAP] 尝试使用代理: {proxy}")
        # 识别不同格式的代理
        try:
            if isinstance(proxy, str) and proxy.startswith('socks'):
                ip = proxy.split('//')[1].split(':')[0]
                print(f"[IMAP] SOCKS代理IP: {ip}")
            elif isinstance(proxy, dict):
                print(f"[IMAP] 字典格式代理，IMAP不直接支持")
                # 可以从字典中寻找SOCKS代理
                for protocol, url in proxy.items():
                    if 'socks' in protocol or 'socks' in url:
                        print(f"[IMAP] 发现SOCKS代理: {url}")
        except Exception as e:
            print(f"[IMAP] 解析代理时出错: {e}")
    else:
        print("[IMAP] 不使用代理，直接连接")
        
    imap = imaplib.IMAP4_SSL("outlook.office365.com", 993, ssl_context=ssl.create_default_context())
    auth = f"user={email_addr}\x01auth=Bearer {token}\x01\x01"
    imap.authenticate("XOAUTH2", lambda _: auth.encode())
    print(f"[IMAP] 已连接到服务器: outlook.office365.com, 用户: {email_addr}")
    return imap

# ---------- Graph 取邮件 ----------
async def graph_messages(token, folder, top, headers=None, proxy=None):
    if headers is None:
        headers = {}
    headers["Authorization"] = f"Bearer {token}"
    url = f"{GRAPH_ROOT}/me/mailFolders/{folder}/messages?$top={top}&$orderby=receivedDateTime desc"
    data = await _get(url, headers, proxy=proxy)
    if not data:
        return []
    res = []
    for m in data.get("value", []):
        res.append({
            "send":    m["from"]["emailAddress"]["address"],
            "subject": m["subject"],
            "text":    m.get("bodyPreview", ""),
            "html":    m["body"]["content"],
            "date":    m["receivedDateTime"],
        })
    return res

# ---------- IMAP 取邮件 ----------
def imap_messages(token, email_addr, folder, top, proxy=None):
    print(f"[IMAP] 开始获取邮件: 邮箱={email_addr}, 文件夹={folder}, 条数={top}")
    try:
        imap = _imap_conn(email_addr, token, proxy=proxy)
        
        # 检查可用文件夹
        print("[IMAP] 列出可用文件夹...")
        status, folder_list = imap.list()
        if status != 'OK':
            print(f"[IMAP] 获取文件夹列表失败: {status}")
        else:
            print(f"[IMAP] 可用文件夹列表:")
            for f in folder_list:
                folder_info = f.decode('utf-8') if isinstance(f, bytes) else str(f)
                print(f"[IMAP] - {folder_info}")
        
        # 尝试打开文件夹
        print(f"[IMAP] 尝试打开文件夹: {folder}")
        status, box_info = imap.select(folder, readonly=True)
        
        if status != 'OK':
            print(f"[IMAP] 打开文件夹失败: {status}, {box_info}")
            # 尝试使用其他可能的垃圾邮件文件夹名称
            if folder.upper() in ['JUNK', 'JUNK EMAIL']:
                alt_folders = ['Junk Email', 'JunkEmail', 'Spam', 'Bulk Mail']
                for alt_folder in alt_folders:
                    print(f"[IMAP] 尝试备选文件夹: {alt_folder}")
                    status, box_info = imap.select(alt_folder, readonly=True)
                    if status == 'OK':
                        print(f"[IMAP] 成功打开备选文件夹: {alt_folder}")
                        folder = alt_folder
                        break
            
            if status != 'OK':
                print("[IMAP] 所有文件夹尝试失败，无法获取邮件")
                imap.logout()
                return []
        
        print(f"[IMAP] 成功打开文件夹: {folder}, 信息: {box_info}")
        
        # 搜索邮件
        _, ids_bytes = imap.search(None, "ALL")
        if not ids_bytes[0]:
            print("[IMAP] 文件夹为空，无邮件")
            imap.logout()
            return []
            
        ids = ids_bytes[0].split()[-top:]
        print(f"[IMAP] 找到 {len(ids)} 封邮件，处理最新 {len(ids)} 封")
        
        res = []
        for i, num in enumerate(reversed(ids)):
            print(f"[IMAP] 获取第 {i+1}/{len(ids)} 封邮件...")
            _, raw = imap.fetch(num, "(RFC822)")
            msg = email.message_from_bytes(raw[0][1])
            subject = email.header.make_header(email.header.decode_header(msg.get("Subject"))).__str__()
            frm = email.utils.parseaddr(msg.get("From"))[1]
            date = msg.get("Date")
            print(f"[IMAP] 邮件标题: {subject}")
            
            plain, html_part = "", ""
            if msg.is_multipart():
                for part in msg.walk():
                    ctype = part.get_content_type()
                    if ctype == "text/plain" and not plain:
                        try:
                            plain = part.get_payload(decode=True).decode(errors="ignore")
                        except:
                            print("[IMAP] 解析纯文本内容失败")
                    if ctype == "text/html" and not html_part:
                        try:
                            html_part = part.get_payload(decode=True).decode(errors="ignore")
                        except:
                            print("[IMAP] 解析HTML内容失败")
            else:
                try:
                    body = msg.get_payload(decode=True).decode(errors="ignore")
                    if msg.get_content_type() == "text/html":
                        html_part = body
                    else:
                        plain = body
                except:
                    print("[IMAP] 解析邮件内容失败")
            
            res.append({"send": frm, "subject": subject, "text": plain, "html": html_part, "date": date})
        
        print(f"[IMAP] 成功获取 {len(res)} 封邮件")
        imap.logout()
        return res
    except Exception as e:
        print(f"[IMAP] 获取邮件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

# ---------- IMAP 删除 ----------
def imap_delete(token, email_addr, folder, proxy=None):
    try:
        imap = _imap_conn(email_addr, token, proxy=proxy)
        imap.select(folder, readonly=False)
        _, ids_bytes = imap.search(None, "ALL")
        if not ids_bytes[0]:
            imap.logout()
            return 0
        id_list = ids_bytes[0].decode().split()
        imap.store(",".join(id_list), "+FLAGS", "\\Deleted")
        imap.expunge()
        imap.logout()
        return len(id_list)
    except Exception as e:
        print("Del error:", e)
        return 0

# ---------- SMTP 发送 ----------
async def smtp_send(token, email_addr, to_addr, subject, text, html, proxy=None):
    # SMTP不直接支持HTTP代理，但可以使用SOCKS代理
    if proxy:
        print(f"[SMTP] 尝试使用代理: {proxy}")
        # 识别不同格式的代理
        try:
            if isinstance(proxy, str) and proxy.startswith('socks'):
                ip = proxy.split('//')[1].split(':')[0]
                print(f"[SMTP] SOCKS代理IP: {ip}")
            elif isinstance(proxy, dict):
                print(f"[SMTP] 字典格式代理，SMTP不直接支持")
                # 可以从字典中寻找SOCKS代理
                for protocol, url in proxy.items():
                    if 'socks' in protocol or 'socks' in url:
                        print(f"[SMTP] 发现SOCKS代理: {url}")
        except Exception as e:
            print(f"[SMTP] 解析代理时出错: {e}")
    else:
        print("[SMTP] 不使用代理，直接连接")
        
    msg = EmailMessage()
    msg["From"], msg["To"], msg["Subject"] = email_addr, to_addr, subject
    if html:
        msg.add_alternative(html, subtype="html")
    else:
        msg.set_content(text)
    smtp = aiosmtplib.SMTP(hostname="smtp.office365.com", port=587, start_tls=True)
    print(f"[SMTP] 连接到服务器: smtp.office365.com")
    await smtp.connect()
    await smtp.authenticate(email_addr, token, mechanism="XOAUTH2")
    print(f"[SMTP] 已认证用户: {email_addr}")
    await smtp.send_message(msg)
    print(f"[SMTP] 邮件已发送至: {to_addr}")
    await smtp.quit()
