# 微软邮箱管理系统

这是一个简化版的微软邮箱管理系统，支持使用OAuth2进行邮箱访问和邮件获取。

## 功能

- 邮箱账号管理：添加、删除邮箱账号
- 查看收件箱/垃圾箱邮件
- 查看邮件详情

## 安装与运行

1. 安装依赖项：

```bash
pip install -r requirements.txt
```

2. 运行应用：

```bash
python main.py
```

3. 访问应用：

浏览器打开 http://localhost:8000

## API 接口

### 获取邮件列表

- **URL**: `/api/mail-all`
- **方法**: `GET`
- **参数**:
  - `refresh_token`: OAuth2刷新令牌
  - `client_id`: 应用客户端ID
  - `email`: 邮箱地址
  - `mailbox`: 邮箱文件夹 (INBOX 或 Junk)
  - `response_type`: 响应格式 (json 或 html)
  - `password`: 访问密码（可选）

### 获取最新邮件

- **URL**: `/api/mail-new`
- **方法**: `GET`
- **参数**: 同上

## 安全配置

可以通过环境变量`PASSWORD`设置访问密码：

```bash
# Windows
set PASSWORD=your_password
python main.py

# Linux/macOS
export PASSWORD=your_password
python main.py
```

## 代理设置

如果您在中国大陆或其他无法直接访问Microsoft API的地区，可以通过设置代理来解决连接问题：

### Windows

```bash
set HTTP_PROXY=http://your_proxy_server:port
set HTTPS_PROXY=http://your_proxy_server:port
python main.py
```

### Linux/macOS

```bash
export HTTP_PROXY=http://your_proxy_server:port
export HTTPS_PROXY=http://your_proxy_server:port
python main.py
```

示例：

```bash
# 使用本地HTTP代理
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
python main.py
``` 