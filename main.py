# -*- coding: utf-8 -*-
from fastapi import FastAPI, Body, Query
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import random
import httpx
import os
import glob
from fake_useragent import UserAgent
from utils_mail import (
    token_graph, token_imap, graph_messages, imap_messages,
    imap_delete, smtp_send
)

# 全局邮箱存储
email_accounts = []

# 邮箱加载功能
def load_email_accounts():
    """从邮箱存放文件夹加载所有邮箱账户"""
    global email_accounts
    email_accounts.clear()
    
    mailbox_dir = "邮箱存放"
    if not os.path.exists(mailbox_dir):
        print(f"[邮箱加载] 邮箱存放目录不存在: {mailbox_dir}")
        return
    
    # 查找所有txt文件
    txt_files = glob.glob(os.path.join(mailbox_dir, "*.txt"))
    print(f"[邮箱加载] 找到 {len(txt_files)} 个邮箱文件")
    
    total_loaded = 0
    for file_path in txt_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                file_loaded = 0
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # 解析邮箱格式: email----password----client_id----refresh_token
                        parts = line.split('----')
                        if len(parts) >= 4:
                            email = parts[0].strip()
                            password = parts[1].strip()
                            client_id = parts[2].strip()
                            refresh_token = parts[3].strip()
                            
                            if email and client_id and refresh_token:
                                email_accounts.append({
                                    'email': email,
                                    'password': password,
                                    'client_id': client_id,
                                    'refresh_token': refresh_token,
                                    'file': os.path.basename(file_path),
                                    'line': line_num
                                })
                                file_loaded += 1
                    except Exception as e:
                        print(f"[邮箱加载] 解析第{line_num}行失败 ({file_path}): {e}")
                        
                print(f"[邮箱加载] {os.path.basename(file_path)}: 加载了 {file_loaded} 个邮箱")
                total_loaded += file_loaded
                
        except Exception as e:
            print(f"[邮箱加载] 读取文件失败 {file_path}: {e}")
    
    print(f"[邮箱加载] 总共加载了 {total_loaded} 个邮箱账户")

# 启动时加载邮箱
print("[系统启动] 开始加载邮箱账户...")
load_email_accounts()

# 初始化 UserAgent 随机生成器
ua = UserAgent()

# 代理池配置 - 确保格式正确
proxy_pool = [
    None,  # 无代理
    "http://127.0.0.1:7897",  # http代理
    # 不使用字典格式，该版本httpx不支持
]

# 查询真实IP及地理位置
async def get_ip_location(ip):
    # 即使是本地代理地址，也尝试获取真实出口IP
    try:
        # 使用代理请求ip查询服务，获取实际出口IP
        proxy = None
        if ip == "127.0.0.1" or ip == "localhost":
            # 如果是本地IP，使用相应代理配置
            proxy_port = None
            # 从代理字符串中提取端口
            for p in proxy_pool:
                if p and "127.0.0.1" in p:
                    parts = p.split(":")
                    if len(parts) > 1:
                        proxy_port = parts[-1]
                        proxy = p
                        break
            
            if proxy_port:
                print(f"[IP] 通过本地代理端口 {proxy_port} 检测真实IP...")
            else:
                return "本地代理 (无法确定实际出口)"
        
        # 使用当前代理请求IP检测服务
        async with httpx.AsyncClient(proxy=proxy, timeout=10) as client:
            # 尝试获取真实IP
            response = await client.get("https://api.ipify.org?format=json")
            if response.status_code == 200:
                data = response.json()
                real_ip = data.get("ip", ip)
                print(f"[IP] 检测到真实出口IP: {real_ip}")
                
                # 获取IP地理位置
                response = await client.get(f"https://ipapi.co/{real_ip}/json/")
                if response.status_code == 200:
                    data = response.json()
                    return f"{data.get('city', '未知城市')}, {data.get('region', '未知地区')}, {data.get('country_name', '未知国家')}"
                
                # 备用API
                response = await client.get(f"https://ip-api.com/json/{real_ip}")
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "success":
                        return f"{data.get('city', '未知城市')}, {data.get('regionName', '未知地区')}, {data.get('country', '未知国家')}"
        
        # 如果上面的方法都失败，直接查询输入的IP
        async with httpx.AsyncClient(timeout=5) as client:
            response = await client.get(f"https://ipapi.co/{ip}/json/")
            if response.status_code == 200:
                data = response.json()
                return f"{data.get('city', '未知城市')}, {data.get('region', '未知地区')}, {data.get('country_name', '未知国家')}"
                
        return "无法获取位置信息"
    except Exception as e:
        print(f"[IP] 查询位置出错: {str(e)}")
        return f"查询位置出错"

# 获取随机UA
def get_random_ua():
    random_ua = ua.random
    print(f"[UA] 使用随机UA: {random_ua}")
    return random_ua

# 获取随机代理
def get_random_proxy():
    proxy = random.choice(proxy_pool)
    if proxy:
        print(f"[PROXY] 使用代理: {proxy}")
        # 尝试提取IP地址
        try:
            # 根据代理类型进行不同处理
            if isinstance(proxy, str):
                if proxy.startswith('http://') or proxy.startswith('https://'):
                    ip = proxy.split('//')[1].split(':')[0]
                elif proxy.startswith('socks5://'):
                    ip = proxy.split('//')[1].split(':')[0]
                else:
                    ip = proxy
                print(f"[PROXY] 代理IP: {ip}")
        except Exception as e:
            print(f"[PROXY] 无法解析代理IP: {e}")
    else:
        print("[PROXY] 不使用代理")
    return proxy


app = FastAPI(title="msOauth2api-python")
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_methods=["*"], allow_headers=["*"])
app.mount("static", StaticFiles(directory="static"), name="static")


@app.get("/", response_class=HTMLResponse)
async def home():
    with open("static/mail.html", "r", encoding="utf-8") as f:
        return f.read()


# ---------- 文件夹映射 ----------
def graph_box(name: str) -> str:
    """Graph API 文件夹名"""
    if name.upper() == "INBOX":
        folder = "inbox"
    else:
        folder = "junkemail"
    print(f"[FOLDER] Graph API 映射: {name} -> {folder}")
    return folder


def imap_box(name: str) -> str:
    """IMAP 文件夹名"""
    if name.upper() == "INBOX":
        folder = "INBOX"
    else:
        folder = "Junk"
    print(f"[FOLDER] IMAP 映射: {name} -> {folder}")
    return folder


# ---------- 统一获取 ----------
async def get_msgs(rt, cid, email_addr, raw_box, top):
    print(f"[GET_MSGS] 开始获取邮件: 邮箱={email_addr}, 文件夹={raw_box}, 条数={top}")
    # 随机UA和代理
    headers = {"User-Agent": get_random_ua()}
    proxy = get_random_proxy()
    
    # 直接使用IMAP (不尝试GraphAPI)
    print("[GET_MSGS] 使用IMAP获取邮件...")
    token = await token_imap(rt, cid, headers=headers, proxy=proxy)
    if token:
        print("[GET_MSGS] 成功获取IMAP令牌")
        mapped_box = imap_box(raw_box)
        return imap_messages(token, email_addr, mapped_box, top, proxy=proxy)
    
    print("[GET_MSGS] IMAP令牌获取失败，无法获取邮件")
    return []


# ---------- 获取全部 ----------
@app.get("/api/mail-all")
async def mail_all(
    refresh_token: str = Query(...), client_id: str = Query(...),
    email: str = Query(...), mailbox: str = Query(...)
):
    # 随机UA和代理
    headers = {"User-Agent": get_random_ua()}
    proxy = get_random_proxy()
    
    # 如果有代理，查询IP地理位置
    if proxy and isinstance(proxy, str):
        try:
            ip = proxy.split('//')[1].split(':')[0] if '//' in proxy else proxy
            location = await get_ip_location(ip)
            print(f"[PROXY] IP所在地: {location}")
        except Exception as e:
            print(f"[PROXY] 查询IP位置失败: {e}")
    
    # 获取邮件
    return await get_msgs(refresh_token, client_id, email, mailbox, top=10000)


# ---------- 获取最新 ----------
@app.get("/api/mail-new")
async def mail_new(
    refresh_token: str = Query(...), client_id: str = Query(...),
    email: str = Query(...), mailbox: str = Query(...),
    response_type: str = Query("json")
):
    # 随机UA和代理
    headers = {"User-Agent": get_random_ua()}
    proxy = get_random_proxy()
    
    # 如果有代理，查询IP地理位置
    if proxy and isinstance(proxy, str):
        try:
            ip = proxy.split('//')[1].split(':')[0] if '//' in proxy else proxy
            location = await get_ip_location(ip)
            print(f"[PROXY] IP所在地: {location}")
        except Exception as e:
            print(f"[PROXY] 查询IP位置失败: {e}")
    
    # 获取邮件
    msgs = await get_msgs(refresh_token, client_id, email, mailbox, top=1)
    msg = msgs[0] if msgs else {}
    if response_type == "html" and msg:
        body_html = msg["html"] or msg["text"].replace("\n", "<br>")
        return HTMLResponse(
            f"""
            <h1>{msg['subject']}</h1>
            <h3>{msg['send']} | {msg['date']}</h3>
            <div>{body_html}</div>
            """,
            status_code=200,
        )
    return msg


# ---------- 清空收件箱 ----------
@app.get("/api/process-inbox")
async def clear_inbox(refresh_token: str, client_id: str, email: str):
    headers = {"User-Agent": get_random_ua()}
    proxy = get_random_proxy()
    
    # 如果有代理，查询IP地理位置
    if proxy and isinstance(proxy, str):
        try:
            ip = proxy.split('//')[1].split(':')[0] if '//' in proxy else proxy
            location = await get_ip_location(ip)
            print(f"[PROXY] IP所在地: {location}")
        except Exception as e:
            print(f"[PROXY] 查询IP位置失败: {e}")
    
    token = await token_imap(refresh_token, client_id, headers=headers, proxy=proxy)
    deleted = imap_delete(token, email, "INBOX", proxy=proxy) if token else 0
    return {"deleted": deleted}


# ---------- 清空垃圾箱 ----------
@app.get("/api/process-junk")
async def clear_junk(refresh_token: str, client_id: str, email: str):
    headers = {"User-Agent": get_random_ua()}
    proxy = get_random_proxy()
    
    # 如果有代理，查询IP地理位置
    if proxy and isinstance(proxy, str):
        try:
            ip = proxy.split('//')[1].split(':')[0] if '//' in proxy else proxy
            location = await get_ip_location(ip)
            print(f"[PROXY] IP所在地: {location}")
        except Exception as e:
            print(f"[PROXY] 查询IP位置失败: {e}")
    
    token = await token_imap(refresh_token, client_id, headers=headers, proxy=proxy)
    deleted = imap_delete(token, email, "Junk", proxy=proxy) if token else 0
    return {"deleted": deleted}


# ---------- 发送邮件 ----------
@app.post("/api/send-mail")
async def send_mail(
    refresh_token: str = Body(...), client_id: str = Body(...),
    email: str = Body(...), to: str = Body(...),
    subject: str = Body(...), text: str | None = Body(None),
    html: str | None = Body(None)
):
    headers = {"User-Agent": get_random_ua()}
    proxy = get_random_proxy()
    
    # 如果有代理，查询IP地理位置
    if proxy and isinstance(proxy, str):
        try:
            ip = proxy.split('//')[1].split(':')[0] if '//' in proxy else proxy
            location = await get_ip_location(ip)
            print(f"[PROXY] IP所在地: {location}")
        except Exception as e:
            print(f"[PROXY] 查询IP位置失败: {e}")
    
    token = await token_graph(refresh_token, client_id, headers=headers, proxy=proxy) or await token_imap(refresh_token,
                                                                                                           client_id,
                                                                                                           headers=headers,
                                                                                                           proxy=proxy)
    if not token:
        return {"error": "token_error"}
    await smtp_send(token, email, to, subject, text or "", html or "", proxy=proxy)
    return {"status": "ok"}


# ---------- 邮箱管理 ----------
@app.get("/api/emails")
async def get_emails():
    """获取已加载的邮箱列表"""
    return {
        "total": len(email_accounts),
        "emails": [
            {
                "email": account["email"],
                "password": account["password"],
                "client_id": account["client_id"],
                "refresh_token": account["refresh_token"],
                "file": account["file"],
                "line": account["line"],
                "has_password": bool(account["password"]),
                "has_client_id": bool(account["client_id"]),
                "has_refresh_token": bool(account["refresh_token"])
            }
            for account in email_accounts
        ]
    }


@app.get("/api/emails/{email_address}")
async def get_email_account(email_address: str):
    """根据邮箱地址获取账户信息"""
    for account in email_accounts:
        if account["email"] == email_address:
            return {
                "email": account["email"],
                "client_id": account["client_id"],
                "refresh_token": account["refresh_token"],
                "file": account["file"],
                "line": account["line"]
            }
    return {"error": "Email not found"}


@app.post("/api/emails/reload")
async def reload_emails():
    """重新加载邮箱账户"""
    print("[API] 收到重新加载邮箱请求")
    load_email_accounts()
    return {
        "message": "邮箱账户已重新加载",
        "total": len(email_accounts)
    }


@app.get("/api/emails/random")
async def get_random_email():
    """随机获取一个邮箱账户"""
    if not email_accounts:
        return {"error": "No email accounts available"}
    
    account = random.choice(email_accounts)
    return {
        "email": account["email"],
        "client_id": account["client_id"],
        "refresh_token": account["refresh_token"]
    }


# ---------- 启动 ----------
if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
