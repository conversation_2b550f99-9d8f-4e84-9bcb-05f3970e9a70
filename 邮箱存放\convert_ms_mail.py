# -*- coding: utf-8 -*-
"""
将每行格式：
A----B----C----D
转换为：
A----B----D----C
"""

input_file = '10acc.txt'
output_file = f'{input_file}副本.txt'

with open(input_file, 'r', encoding='utf-8') as fin, open(output_file, 'w', encoding='utf-8') as fout:
    for line in fin:
        line = line.strip('\r\n')
        if not line:
            continue
        parts = line.split('----')
        if len(parts) == 4:
            # 交换第3和第4段
            new_line = f'{parts[0]}----{parts[1]}----{parts[3]}----{parts[2]}'
            fout.write(new_line + '\n')
        else:
            # 格式不对，原样输出
            fout.write(line + '\n')
print(f'转换完成，输出文件：{output_file}') 